
import os
config = {
    **os.environ
}

from celery import Celery
from datetime import datetime, timedelta
import json
import logging
import time
import os
import sys
from db_driver import ConnectionPool
from mt5linux import MetaTrader5
from helper import get_time_market_open, is_market_open
from ctrader_open_api import Client, Protobuf, TcpProtocol, Auth, EndPoints
from ctrader_open_api.messages.OpenApiCommonMessages_pb2 import *
from ctrader_open_api.messages.OpenApiMessages_pb2 import *
from ctrader_open_api.messages.OpenApiModelMessages_pb2 import *
from celery.signals import worker_process_init
import numpy as np
import pytz

# Configure logging to send debug logs to stdout for Celery workers
# Get log level from environment variable or default to DEBUG
log_level_name = os.environ.get('LOG_LEVEL', 'INFO')
log_level = getattr(logging, log_level_name.upper(), logging.DEBUG)

# Configure logging
logging.basicConfig(
    level=log_level,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

mt5 = None

def safe_convert_to_python_type(value):
    """
    Convert numpy types to native Python types for JSON serialization.
    This prevents Celery JSON serialization errors with numpy.int64, etc.
    """
    if isinstance(value, (np.integer, np.int64, np.int32)):
        return int(value)
    elif isinstance(value, (np.floating, np.float64, np.float32)):
        return float(value)
    elif isinstance(value, np.bool_):
        return bool(value)
    elif isinstance(value, np.ndarray):
        return value.tolist()
    else:
        return value

def get_next_working_day_5am_utc7():
    """
    Returns the next 5:00 AM UTC+7 on a working day (Monday to Friday).
    If today is a working day and it's before 5:00 AM, returns today at 5:00 AM.
    Otherwise, returns the next working day at 5:00 AM.
    """
    # Define UTC+7 timezone
    utc7_tz = pytz.timezone(config.get('TZ', 'Asia/Ho_Chi_Minh'))  # UTC+7

    # Get current time in UTC+7
    now = datetime.now(utc7_tz)

    # Create 5:00 AM today in UTC+7
    target_time = now.replace(hour=5, minute=0, second=0, microsecond=0)

    # Check if today is a working day (Monday=0, Sunday=6)
    # Working days are Monday (0) to Friday (4)
    current_weekday = now.weekday()

    # If it's a working day and before 5:00 AM, return today at 5:00 AM
    if current_weekday < 5 and now < target_time:
        return target_time

    # Otherwise, find the next working day
    days_to_add = 1
    next_day = now + timedelta(days=days_to_add)

    # Keep adding days until we find a working day
    while next_day.weekday() >= 5:  # Saturday (5) or Sunday (6)
        days_to_add += 1
        next_day = now + timedelta(days=days_to_add)

    # Return 5:00 AM on the next working day
    return next_day.replace(hour=5, minute=0, second=0, microsecond=0)

# This code runs when the worker starts up, but not when imported by main.py
@worker_process_init.connect
def init_worker(**kwargs):
    global connection_pool, mt5
    # Initialize resources only for the worker
    connection_pool = ConnectionPool(connection_string=config['CONNECTION_STRING'])
    try:
        mt5 = MetaTrader5(host=config['MT5_URL'], port=8001)
    except Exception as e:
        logger.error("MT5 first init fail: %s", str(e))
        # Try to send escape signal to mt5 container
        try:
            from internal_requests import send_mt5_escape_signal
            res = False
            res = send_mt5_escape_signal(config)
        except Exception as escape_error:
            logger.error(f"Error sending escape signal to MT5 container: {str(escape_error)}")
        if res:
            try:
                mt5 = MetaTrader5(host=config['MT5_URL'], port=8001)
                logger.info("Worker initialized with MT5 and connection pool")
            except Exception as e:
                logger.error("MT5 second init worker connect fail: %s. Continue", str(e))


# Create Celery app (this still needs to be available to both worker and main.py)
app = Celery('tasks', broker=config['CELERY_BROKER_URL'])

# Define a Celery task
@app.task(bind=True, max_retries=5, default_retry_delay=2)
def do_pending_changes_slave_deals(self, order_id, accountable_id, accountable_type, mt5_info):
    # Read request changes in db
    slave_orders = connection_pool.find_slave_deal(order_id, accountable_id, accountable_type)
    if slave_orders.empty:
        logger.info('Not found any slave order to do pending changes')
        return
    if slave_orders['status'][0] != ConnectionPool.ORDER_STATUS['waiting']:
        logger.info('Status order is not waiting, will not do anything')
        return
    if slave_orders.pending_changes[0] is None:
        logger.info('Pending changes empty, please fix this data')
        return
    request = json.loads(slave_orders.pending_changes[0])
    mt5_info_df = connection_pool.find_slave_account(id=slave_orders.accountable_id[0])
    if mt5_info_df.empty:
        logger.info('Missing mt5 info account in db')
        return
    if not mt5_init(login=mt5_info_df.login[0], server=mt5_info_df.server[0], password=mt5_info_df.password[0]):
        self.retry(countdown=1)

    time.sleep(0.33)
    # market order
    if request['action'] == mt5.TRADE_ACTION_DEAL:
        # Check closing order volume valid
        if request.get('position'):
            position = mt5.positions_get(ticket=request.get('position'))
            if position is None:
                logger.info('Position already closed')
                return
            volume_current = position[0].volume
            if volume_current is None:
                logger.info('Not found position in mt5')
                return
            if request['volume'] > volume_current:
                logger.info('Volume close is greater. Set to current volume')
                request['volume'] = volume_current
        if str(mt5_info['pending_market_enabled']) == 'True' or int(mt5_info['pending_market_enabled']) == 1:
            request['price'] = mt5.symbol_info_tick(request['symbol']).ask if request['type'] == mt5.ORDER_TYPE_BUY else mt5.symbol_info_tick(request['symbol']).bid
        else:
            logger.info('PENDING_MARKET_ENABLED disabled: Not handle pending market order')
            if request.get('position'):
                status = ConnectionPool.ORDER_STATUS['open']
            else:
                status = ConnectionPool.ORDER_STATUS['closed']
            logger.info("Reapply old status for slave order")
            connection_pool.update_status_slave_order(slave_orders['order_ota'][0], slave_orders['account_id'][0], slave_orders['ota'][0], status)
            return
    mt5_order_send.delay(mt5_info_df.to_dict(orient='records')[0], request, request['symbol'], slave_orders.to_dict(orient='records')[0])


@app.task(bind=True, max_retries=3, default_retry_delay=1)
def mt5_order_send(self, mt5_info, request, symbolName, slave_order):
    if slave_order.get('status') == ConnectionPool.ORDER_STATUS['waiting']:
        slave_order_df = connection_pool.find_slave_deal(slave_order['order_id'], slave_order['accountable_id'], slave_order['accountable_type'])
        if slave_order_df.empty:
            logger.info('Slave order not found')
            return
        if slave_order_df.status[0] != ConnectionPool.ORDER_STATUS['waiting']:
            logger.info('Slave order status is not waiting anymore')
            return
        logger.info('Pending changes request: ' + str(request))
        existing_pending_changes = json.loads(slave_order_df['pending_changes'][0])
        if request['action'] == mt5.TRADE_ACTION_REMOVE:
            # is open position waiting
            if existing_pending_changes['action'] == mt5.TRADE_ACTION_PENDING or existing_pending_changes['action'] == mt5.TRADE_ACTION_DEAL and existing_pending_changes.get('position') is None:
                connection_pool.update_status_slave_order(slave_order_df['order_id'][0], slave_order_df['accountable_id'][0], slave_order_df['accountable_type'][0], ConnectionPool.ORDER_STATUS['cancelled'])
            else:
                connection_pool.apply_pending_changes_slave_deal(safe_convert_to_python_type(slave_order_df['id'][0]), json.dumps(request))
            return
        if existing_pending_changes['action'] == mt5.TRADE_ACTION_SLTP:
            if existing_pending_changes['sl'] != request['sl'] or existing_pending_changes['tp'] != request['tp']:
                logger.info('SL TP changed. Update new SL TP to pending changes waiting')
                existing_pending_changes['sl'] = request['sl']
                existing_pending_changes['tp'] = request['tp']
                connection_pool.apply_pending_changes_slave_deal(safe_convert_to_python_type(slave_order_df['id'][0]), json.dumps(existing_pending_changes))
        if existing_pending_changes['action'] == mt5.TRADE_ACTION_DEAL:
            # if close trade waiting? same as cancel
            if existing_pending_changes.get('position') is None and request.get('position') is not None:
                remaining_volume = existing_pending_changes['volume'] - request['volume']
                if remaining_volume < 0:
                    logger.info('Invalid closing request: Volume closing is greater than current')
                if remaining_volume == 0.0:
                    # Cancel existing waiting order
                    logger.info('Close all volume. Cancel existing waiting order')
                    connection_pool.update_status_slave_order(slave_order_df['order_id'][0], slave_order_df['accountable_id'][0], slave_order_df['accountable_type'][0], ConnectionPool.ORDER_STATUS['cancelled'])
                if remaining_volume > 0: # Close partial
                    # Update new volume for existing slave order
                    logger.info('Close partial volume. Update new volume for existing slave order')
                    existing_pending_changes['volume'] = remaining_volume
                    connection_pool.apply_pending_changes_slave_deal(safe_convert_to_python_type(slave_order_df['id'][0]), json.dumps(existing_pending_changes))
            # Closing more existing position
            if existing_pending_changes.get('position') is not None and request.get('position') is not None:
                existing_pending_changes['volume'] = existing_pending_changes['volume'] + request['volume']
                connection_pool.apply_pending_changes_slave_deal(safe_convert_to_python_type(slave_order_df['id'][0]), json.dumps(existing_pending_changes))
            if request['action'] == mt5.TRADE_ACTION_SLTP:
                if existing_pending_changes['sl'] != request['sl'] or existing_pending_changes['tp'] != request['tp']:
                    existing_pending_changes['sl'] = request['sl']
                    existing_pending_changes['tp'] = request['tp']
                    connection_pool.apply_pending_changes_slave_deal(safe_convert_to_python_type(slave_order_df['id'][0]), json.dumps(existing_pending_changes))
        if existing_pending_changes['action'] in (mt5.TRADE_ACTION_PENDING, mt5.TRADE_ACTION_MODIFY):
            if request['action'] == mt5.TRADE_ACTION_MODIFY:
                existing_pending_changes['volume'] = request['volume']
                existing_pending_changes['price'] = request['price']
                existing_pending_changes['sl'] = request['sl']
                existing_pending_changes['tp'] = request['tp']
            if request['action'] == mt5.TRADE_ACTION_SLTP:
                existing_pending_changes['sl'] = request['sl']
                existing_pending_changes['tp'] = request['tp']
            connection_pool.apply_pending_changes_slave_deal(safe_convert_to_python_type(slave_order_df['id'][0]), json.dumps(existing_pending_changes))
        if existing_pending_changes['action'] == mt5.TRADE_ACTION_SLTP:
            connection_pool.apply_pending_changes_slave_deal(safe_convert_to_python_type(slave_order_df['id'][0]), json.dumps(request))
    else:
        if request['action'] == mt5.TRADE_ACTION_SLTP:
            slave_order_df = connection_pool.find_slave_deal(slave_order['order_id'], slave_order['accountable_id'], slave_order['accountable_type'])
            # Check sl tp slave deal in db if need to be updated
            if float(slave_order_df['sl'][0]) == request['sl'] and float(slave_order_df['tp'][0]) == request['tp']:
                logger.info('SL TP no changes. Skip update')
                return

        if not mt5_init(mt5_info['login'], mt5_info['server'], mt5_info['password']):
            self.retry(countdown=1)
        # Get symbol info
        time.sleep(0.33)
        symbol_info=mt5.symbol_info(symbolName)
        if symbol_info is None:
            logger.info(f"Can not get symbol info MT5 or symbol is not avaiable: {symbolName}")
            return
        # Add symbol to watchlist if it's not
        if symbol_info.select is False:
            selected = mt5.symbol_select(symbolName, True)
            if selected:
                logger.info(f"Added {symbolName} to watchlist")
            else:
                logger.info(f"Fail to add {symbolName} to watchlist")
                return
        # Send request
        logger.info(f"Request MT5: {str(request)}")
        time.sleep(0.22)

        if request['action'] == mt5.TRADE_ACTION_MODIFY and request.get('volume') is not None:
            # Check if change volume order:
            current_order = mt5.orders_get(ticket=request['order'])
            if current_order is None:
                logger.info('Can not find order exists in MT5')
                return
            current_order = current_order[0]
            if current_order.volume_current != request['volume']:
                # Cancel old pending order
                delete_request = {
                    "action": mt5.TRADE_ACTION_REMOVE,
                    "order": request['order'],
                    "magic": 2268
                }
                mt5_order_send.delay(mt5_info, delete_request, symbolName, slave_order)
                create_request = {
                    "action": mt5.TRADE_ACTION_PENDING,
                    "type": current_order.type,
                    "symbol": current_order.symbol,
                    "volume": request['volume'],
                    "price": request['price'],
                    "sl": request['sl'],
                    "tp": request['tp'],
                    "comment": current_order.comment,
                    "magic": 2268,
                    "type_filling": current_order.type_filling,
                    "type_time": current_order.type_time
                }
                mt5_order_send.delay(mt5_info, create_request, symbolName, slave_order)
                return
        if request['action'] == mt5.TRADE_ACTION_DEAL:
            # Check closing order volume valid
            if request.get('position'):
                position = mt5.positions_get(ticket=request.get('position'))
                if position is None or len(position) == 0:
                    logger.info('Position already closed')
                    return
                volume_current = position[0].volume
                if volume_current is None:
                    logger.info('Not found position in mt5')
                    return
                if request['volume'] > volume_current:
                    logger.info('Volume close is greater. Set to current volume')
                    request['volume'] = volume_current
            # Extract filling_mode
            request['type_filling'] = mt5.ORDER_FILLING_IOC
            if request['type'] == mt5.ORDER_TYPE_BUY:
                request['price'] = mt5.symbol_info_tick(symbolName).ask
            else:
                request['price'] = mt5.symbol_info_tick(symbolName).bid
        result = mt5.order_send(request)
        # Handle result
        # symbol_data = ProtoOASymbol.FromString(symbol_data_dumps)
        if result.retcode == mt5.TRADE_RETCODE_MARKET_CLOSED:
            logger.info('Market closed')
            if slave_order is None:
                logger.info('Invalid request: missing slave order')
                return
            if request['action'] == mt5.TRADE_ACTION_DEAL:
                if mt5_info['pending_market_enabled']:
                    logger.info("PENDING_MARKET_ENABLED enable. Market order will be executed when market open")
                    # open new market order
                    if request.get('position') is not None:
                        new_pending_changes(request, slave_order)
                    else: # Close order
                        save_pending_changes_existed_slave_order(request, slave_order)
                else:
                    logger.info('PENDING_MARKET_ENABLED disabled. Ignore market order when market closed')
                    return
            if request['action'] == mt5.TRADE_ACTION_PENDING:
                new_pending_changes(request, slave_order)
            if request['action'] in (mt5.TRADE_ACTION_MODIFY, mt5.TRADE_ACTION_SLTP):
                save_pending_changes_existed_slave_order(request, slave_order)
            future_time = get_next_working_day_5am_utc7()
            future_time = future_time + timedelta(minutes=int(mt5_info['pending_time_offset']))
            logger.info(f"Pending order request order at {str(future_time)}")
            do_pending_changes_slave_deals.apply_async(args=(slave_order['order_id'], slave_order['accountable_id'], slave_order['accountable_type'], mt5_info), eta=future_time)

        else:
            if result.retcode == mt5.TRADE_RETCODE_DONE:
                if request['action'] == mt5.TRADE_ACTION_SLTP:
                    connection_pool.update_sl_tp_slave_deal(safe_convert_to_python_type(slave_order_df['id'][0]), request['sl'], request['tp'])
                    return
                # Change status slave order
                status = None
                if request['action'] == mt5.TRADE_ACTION_PENDING or request['action'] == mt5.TRADE_ACTION_DEAL and request.get('position') is None:
                    slave_order['status'] = ConnectionPool.ORDER_STATUS['executed'] if request['action'] == mt5.TRADE_ACTION_DEAL else ConnectionPool.ORDER_STATUS['open']
                    slave_order['order_id'] = result.order
                    slave_order['position_id'] = result.order
                    slave_order['tp'] = request['tp']
                    slave_order['sl'] = request['sl']
                    connection_pool.insert_slave_deals(slave_order)
                # Check if close trade complete or not
                if request['action'] == mt5.TRADE_ACTION_DEAL and request.get('position') is not None:
                    # Is position still open
                    if mt5.positions_get(ticket=request['position']) is not None:
                        status = ConnectionPool.ORDER_STATUS['open']
                    else:
                        status = ConnectionPool.ORDER_STATUS['closed']
                if request['action'] == mt5.TRADE_ACTION_REMOVE:
                    status = ConnectionPool.ORDER_STATUS['cancelled']
                if status is not None:
                    connection_pool.update_status_slave_order(slave_order['order_id'], slave_order['accountable_id'], slave_order['accountable_type'], status)
            else:
                logger.info(f"Failed result from mt5: {result.comment}")
                trade_action_map = {
                    mt5.TRADE_ACTION_DEAL: 'DEAL MARKET',
                    mt5.TRADE_ACTION_PENDING: 'PENDING',
                    mt5.TRADE_ACTION_MODIFY: 'MODIFY',
                    mt5.TRADE_ACTION_SLTP: 'SLTP',
                    mt5.TRADE_ACTION_REMOVE: 'REMOVE'
                }
                logger.info(f"Trade action: {trade_action_map[request['action']]}")
                if request['action'] == mt5.TRADE_ACTION_DEAL and result.comment in ('No prices', 'Requote'):
                    logger.info('Market order error and retry')
                    self.retry(countdown=0.5)
                if request['action'] in (mt5.TRADE_ACTION_PENDING, mt5.TRADE_ACTION_MODIFY) and result.comment in ('Invalid price') and request['type'] in (mt5.ORDER_TYPE_BUY_LIMIT, mt5.ORDER_TYPE_SELL_LIMIT):
                    is_market_better = False
                    if request['type'] == mt5.ORDER_TYPE_BUY_LIMIT:
                        new_price = mt5.symbol_info_tick(symbolName).ask
                        new_type = mt5.ORDER_TYPE_BUY
                        is_market_better = new_price > request['price']
                    else:
                        new_price = mt5.symbol_info_tick(symbolName).bid
                        new_type = mt5.ORDER_TYPE_SELL
                        is_market_better = new_price < request['price']
                    if is_market_better:
                        logger.info('Market price is better. Send market order instead')
                        new_request = {
                            "action": mt5.TRADE_ACTION_DEAL,
                            "type": new_type,
                            "symbol": symbolName,
                            "volume": request['volume'],
                            "sl": request['sl'],
                            "tp": request['tp'],
                            "magic": 2268,
                            "deviation": 11,
                            "type_filling": mt5.ORDER_FILLING_IOC,
                            "type_time": mt5.ORDER_TIME_GTC
                        }
                        loop_count = 0
                        while True:
                            if request['type'] == mt5.ORDER_TYPE_BUY_LIMIT:
                                new_request['price'] = mt5.symbol_info_tick(symbolName).ask
                            else:
                                new_request['price'] = mt5.symbol_info_tick(symbolName).bid

                            logger.info(f"Request market MT5 instead of pending: {str(new_request)}")
                            result = mt5.order_send(new_request)
                            if result.retcode == mt5.TRADE_RETCODE_DONE:
                                if request['action'] == mt5.TRADE_ACTION_PENDING:
                                    slave_order['status'] = ConnectionPool.ORDER_STATUS['executed']
                                    slave_order['order_id'] = result.order
                                    slave_order['position_id'] = result.order
                                    slave_order['sl'] = request['sl']
                                    slave_order['tp'] = request['tp']
                                    connection_pool.insert_slave_deals(slave_order)
                                else:
                                    # Cancel old pending order
                                    logger.info('New order id/position: ' + str(result.order))
                                    delete_request = {
                                        "action": mt5.TRADE_ACTION_REMOVE,
                                        "order": request['order'],
                                        "magic": 2268
                                    }
                                    res = mt5.order_send(delete_request)
                                    if res.retcode != mt5.TRADE_RETCODE_DONE:
                                        logger.info(f"Failed to cancel old pending order: {res.comment}")

                                    connection_pool.update_order_id_slave_deal(id=slave_order['id'], order_id=result.order)
                                    connection_pool.update_status_slave_order(result.order, slave_order['accountable_id'], slave_order['accountable_type'], ConnectionPool.ORDER_STATUS['executed'])
                                break
                            else:
                                logger.info(f"Failed result from mt5: {result.comment}")
                                loop_count += 1
                                if loop_count >= 3:
                                    logger.info('Retry 3 times to send market order and fail. Stop retry')
                                    return
                                time.sleep(0.5)
                    else:
                        logger.info('Retry send pending order')
                        self.retry(countdown=0.5)


def save_pending_changes_existed_slave_order(request, slave_order):
    slave_order_df = connection_pool.find_slave_deal(slave_order['order_id'], slave_order['accountable_id'], slave_order['accountable_type'])
    if slave_order_df is None:
        logger.info(f"Request error: not found any slave deal: {slave_order['order_id']}")
        return
    connection_pool.apply_pending_changes_slave_deal(safe_convert_to_python_type(slave_order_df['id'][0]), json.dumps(request))


def new_pending_changes(request, slave_order):
    slave_order['status'] = ConnectionPool.ORDER_STATUS['waiting']
    slave_order['pending_changes'] = json.dumps(request)
    logger.info('slave deal info:')
    logger.info(str(slave_order))
    connection_pool.insert_slave_deals(slave_order)
    logger.info('Inserted slave deal with status waiting done')

def mt5_init(login, server, password):
    try:
        global mt5
        # Check mt5 account if logged in
        mt5 = mt5 or MetaTrader5(host=config['MT5_URL'], port=8001)
        if int(mt5.account_info().login) == int(login):
            logger.info("Already logged in MT5 for account")
            return True
        else:
            logger.info("Login MT5 for account %s", str(login))
            raise Exception()
    except:
        # Login user account
        if not mt5.initialize(login=int(login), server=server, password=password):
            logger.info("login mt5 failed for account %s", str(login))
            logger.info("Init MT5 fail then retry")
            return False
    return True

@app.task
def add(a, b):
    logger.info(str(a+b))

@app.task(bind=True, max_retries=3, default_retry_delay=5)
def delay_set_tp_sl(self, position_id, tp, sl, host_info, mt5_info, symbol_name):
    logger.info('Modify open position SL TP')

    slave_orders = connection_pool.get_slave_deal_by_host_position_id(position_id, int(host_info['account_id']), 'CtraderAccount', mt5_info['login'], 'Mt5Account')
    if slave_orders.empty:
        logger.info("Not found any slave order match to modify SL TP")
        return
    # Prepare order request
    request = {
        "action": mt5.TRADE_ACTION_SLTP,
        "position": safe_convert_to_python_type(slave_orders['position_id'][0]),
        "magic": 2268,
        "sl": sl,
        "tp": tp
    }

    slave_order = {
        'order_id': safe_convert_to_python_type(slave_orders['order_id'][0]),
        'accountable_id': safe_convert_to_python_type(slave_orders['accountable_id'][0]),
        'host_deal_id': safe_convert_to_python_type(slave_orders['host_deal_id'][0]),
        'accountable_type': str(slave_orders['accountable_type'][0]),
        'status': safe_convert_to_python_type(slave_orders['status'][0]),
    }
    mt5_order_send.delay(mt5_info, request, symbol_name, slave_order)
